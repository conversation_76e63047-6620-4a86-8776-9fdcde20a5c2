/**
 * Flight Landing Pages Backend JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize backend search and pagination
    initializeBackendSearchAndPagination();
});

/**
 * Backend pagination and search state
 */
const BackendPagination = {
    currentPage: 1,
    itemsPerPage: 20,
    searchTerm: '',
    totalItems: 0,
    filteredItems: [],
    allItems: [],

    // Initialize from URL hash
    initFromHash: function() {
        const hash = window.location.hash.substring(1);
        if (hash) {
            const parts = hash.split(':');
            if (parts.length === 2) {
                this.searchTerm = decodeURIComponent(parts[0]);
                this.currentPage = parseInt(parts[1]) || 1;
            }
        }
    },

    // Update URL hash
    updateHash: function() {
        const hash = this.searchTerm ? `${encodeURIComponent(this.searchTerm)}:${this.currentPage}` : '';
        if (hash) {
            window.location.hash = hash;
        } else {
            history.replaceState(null, null, window.location.pathname + window.location.search);
        }
    }
};

/**
 * Initialize backend search and pagination functionality for destination pairs
 */
function initializeBackendSearchAndPagination() {
    const searchInput = document.getElementById('backend-route-search');
    const clearButton = document.getElementById('clear-backend-search');
    const resultsCount = document.getElementById('backend-results-count');
    const routeItems = document.querySelectorAll('.flight-route-item[data-search-content]');

    if (!searchInput || routeItems.length === 0) {
        return; // No search functionality needed
    }

    // Store all items
    BackendPagination.allItems = Array.from(routeItems);
    BackendPagination.totalItems = routeItems.length;

    // Initialize from URL hash
    BackendPagination.initFromHash();

    // Set initial search term if from hash
    if (BackendPagination.searchTerm) {
        searchInput.value = BackendPagination.searchTerm;
    }

    // Create pagination controls
    createPaginationControls();

    function filterAndPaginateRoutes() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const previousSearchTerm = BackendPagination.searchTerm;

        // If search term changed, reset to page 1
        if (searchTerm !== previousSearchTerm) {
            BackendPagination.currentPage = 1;
        }

        BackendPagination.searchTerm = searchTerm;

        // Show/hide clear button
        if (clearButton) {
            clearButton.style.display = searchTerm ? 'block' : 'none';
        }

        // Filter items
        BackendPagination.filteredItems = BackendPagination.allItems.filter(function(item) {
            const searchContent = item.getAttribute('data-search-content') || '';
            return !searchTerm || searchContent.toLowerCase().includes(searchTerm);
        });

        // Update pagination
        updatePagination();

        // Update URL hash
        BackendPagination.updateHash();
    }

    function clearBackendSearch() {
        searchInput.value = '';
        BackendPagination.searchTerm = '';
        BackendPagination.currentPage = 1;
        searchInput.focus();
        filterAndPaginateRoutes();
    }

    // Event listeners
    searchInput.addEventListener('input', filterAndPaginateRoutes);
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            clearBackendSearch();
        }
    });

    if (clearButton) {
        clearButton.addEventListener('click', clearBackendSearch);
    }

    // Initialize
    filterAndPaginateRoutes();
}

/**
 * Create pagination controls
 */
function createPaginationControls() {
    const routesList = document.querySelector('.flight-routes-list');
    if (!routesList) return;

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'backend-pagination-container mt-3';
    paginationContainer.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div class="backend-pagination-info">
                <small class="text-muted">
                    <select id="backend-items-per-page" class="form-control form-control-sm d-inline-block" style="width: auto;">
                        <option value="10">10 per page</option>
                        <option value="20" selected>20 per page</option>
                        <option value="40">40 per page</option>
                        <option value="80">80 per page</option>
                        <option value="150">150 per page</option>
                    </select>
                </small>
            </div>
            <div class="backend-pagination-controls">
                <nav aria-label="Destination pairs pagination">
                    <ul class="pagination pagination-sm mb-0" id="backend-pagination">
                        <!-- Pagination buttons will be inserted here -->
                    </ul>
                </nav>
            </div>
        </div>
    `;

    // Insert after the routes list
    routesList.parentNode.insertBefore(paginationContainer, routesList.nextSibling);

    // Add event listener for items per page change
    const itemsPerPageSelect = document.getElementById('backend-items-per-page');
    itemsPerPageSelect.addEventListener('change', function() {
        BackendPagination.itemsPerPage = parseInt(this.value);
        BackendPagination.currentPage = 1; // Reset to first page
        updatePagination();
        BackendPagination.updateHash();
    });
}

/**
 * Update pagination display and item visibility
 */
function updatePagination() {
    const totalFiltered = BackendPagination.filteredItems.length;
    const totalPages = Math.ceil(totalFiltered / BackendPagination.itemsPerPage);

    // Ensure current page is valid
    if (BackendPagination.currentPage > totalPages && totalPages > 0) {
        BackendPagination.currentPage = totalPages;
    }
    if (BackendPagination.currentPage < 1) {
        BackendPagination.currentPage = 1;
    }

    // Calculate start and end indices
    const startIndex = (BackendPagination.currentPage - 1) * BackendPagination.itemsPerPage;
    const endIndex = startIndex + BackendPagination.itemsPerPage;

    // Hide all items first
    BackendPagination.allItems.forEach(function(item) {
        item.classList.add('search-hidden');
    });

    // Show items for current page
    const itemsToShow = BackendPagination.filteredItems.slice(startIndex, endIndex);
    itemsToShow.forEach(function(item) {
        item.classList.remove('search-hidden');
    });

    // Update results count
    updateResultsCount(startIndex + 1, Math.min(endIndex, totalFiltered), totalFiltered, BackendPagination.totalItems);

    // Update pagination controls
    updatePaginationControls(totalPages);
}

/**
 * Update results count display
 */
function updateResultsCount(start, end, filtered, total) {
    const resultsCount = document.getElementById('backend-results-count');
    if (!resultsCount) return;

    if (filtered === 0) {
        resultsCount.textContent = `No destination pairs found / from ${total}`;
    } else if (filtered === total) {
        resultsCount.textContent = `Showing ${start}-${end} of ${filtered} destination pairs`;
    } else {
        resultsCount.textContent = `Showing ${start}-${end} of ${filtered} results / from ${total}`;
    }
}

/**
 * Update pagination controls
 */
function updatePaginationControls(totalPages) {
    const paginationContainer = document.getElementById('backend-pagination');
    if (!paginationContainer) return;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHTML = '';
    const currentPage = BackendPagination.currentPage;

    // Previous button
    if (currentPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;
    } else {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </span>
            </li>
        `;
    }

    // Page numbers
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // First page and ellipsis
    if (startPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="1">1</a>
            </li>
        `;
        if (startPage > 2) {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            paginationHTML += `
                <li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>
            `;
        } else {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
    }

    // Last page and ellipsis
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
            </li>
        `;
    }

    // Next button
    if (currentPage < totalPages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;
    } else {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </span>
            </li>
        `;
    }

    paginationContainer.innerHTML = paginationHTML;

    // Add click event listeners to pagination links
    paginationContainer.querySelectorAll('a.page-link[data-page]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.getAttribute('data-page'));
            if (page && page !== BackendPagination.currentPage) {
                BackendPagination.currentPage = page;
                updatePagination();
                BackendPagination.updateHash();
            }
        });
    });
}
